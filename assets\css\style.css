/* Professional Hosting Provider Theme */
:root {
    --primary-orange: #ff6b35;
    --secondary-orange: #e55a2b;
    --dark-bg: #0d1117;
    --darker-bg: #010409;
    --card-bg: #161b22;
    --border-color: #30363d;
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #6e7681;
    --gradient-orange: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius: 8px;
    --radius-lg: 12px;
    --transition: all 0.15s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: rgba(13, 17, 23, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid var(--border-color);
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 18px;
}

.logo img {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 0;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-orange);
}

.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: var(--transition);
    padding: 8px 0;
    margin-top: 8px;
}

.nav-item:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 12px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: rgba(255, 107, 53, 0.1);
    color: var(--primary-orange);
}

.lang-switcher {
    display: flex;
    gap: 8px;
}

.lang-btn {
    padding: 6px 12px;
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.lang-btn:hover,
.lang-btn.active {
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

.mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 20px;
    cursor: pointer;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 24px;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--radius);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    gap: 8px;
}

.btn-primary {
    background: var(--gradient-orange);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-orange);
    border: 1px solid var(--primary-orange);
}

.btn-secondary:hover {
    background: var(--primary-orange);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

/* Sections */
section {
    padding: 80px 0;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    text-align: center;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 64px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Features Grid */
.features {
    background: var(--card-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.feature-card {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 32px;
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-orange);
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: var(--gradient-orange);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 24px;
    color: white;
}

.feature-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing Cards */
.pricing {
    background: var(--darker-bg);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    max-width: 1200px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 32px;
    position: relative;
    transition: var(--transition);
}

.pricing-card.featured {
    border-color: var(--primary-orange);
    transform: scale(1.05);
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-4px);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-orange);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.pricing-card h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.pricing-card .price {
    margin-bottom: 32px;
}

.price-amount {
    font-size: 48px;
    font-weight: 700;
    color: var(--primary-orange);
}

.price-currency {
    font-size: 24px;
    vertical-align: top;
}

.price-period {
    font-size: 16px;
    color: var(--text-secondary);
    margin-left: 8px;
}

.features-list {
    list-style: none;
    margin-bottom: 32px;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: var(--text-secondary);
}

.features-list li i {
    color: var(--primary-orange);
    width: 16px;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    padding: 64px 0 32px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 48px;
    margin-bottom: 48px;
}

.footer-section h3 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-orange);
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        margin: 8px 16px;
        padding: 16px;
        flex-direction: column;
        gap: 16px;
    }

    .nav-menu.active {
        display: flex;
    }

    .dropdown {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
        margin-top: 8px;
        padding: 0;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-4px);
    }
}
