/* CSS Variables for Orange-Dark Theme */
:root {
    --primary-orange: #ff6b35;
    --secondary-orange: #f7931e;
    --dark-bg: #1a1a1a;
    --darker-bg: #0f0f0f;
    --light-text: #ffffff;
    --gray-text: #cccccc;
    --dark-text: #333333;
    --accent-orange: #ff8c42;
    --gradient-orange: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    --shadow-orange: 0 4px 20px rgba(255, 107, 53, 0.3);
    --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.5);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--dark-bg);
    color: var(--light-text);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--gradient-dark);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-orange);
    text-decoration: none;
}

.logo img {
    height: 40px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover {
    color: var(--primary-orange);
    background: rgba(255, 107, 53, 0.1);
}

/* Dropdown Menu */
.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--darker-bg);
    min-width: 200px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-dark);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.nav-item:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: rgba(255, 107, 53, 0.1);
    color: var(--primary-orange);
}

/* Language Switcher */
.language-switcher {
    display: flex;
    gap: 0.5rem;
}

.lang-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid var(--primary-orange);
    color: var(--primary-orange);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.lang-btn:hover,
.lang-btn.active {
    background: var(--primary-orange);
    color: var(--light-text);
}

/* Mobile Menu Toggle */
.mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: var(--gradient-dark);
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding-top: 80px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    background: var(--gradient-orange);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-text);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-orange);
    color: var(--light-text);
    box-shadow: var(--shadow-orange);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-orange);
    border: 2px solid var(--primary-orange);
}

.btn-secondary:hover {
    background: var(--primary-orange);
    color: var(--light-text);
}

.btn-outline {
    background: transparent;
    color: var(--light-text);
    border: 2px solid var(--light-text);
}

.btn-outline:hover {
    background: var(--light-text);
    color: var(--dark-bg);
}

/* Sections */
section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient-orange);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Section */
.features {
    background: var(--darker-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.feature-card p {
    color: var(--gray-text);
}

/* Pricing Section */
.pricing-preview {
    background: var(--dark-bg);
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.pricing-card {
    background: var(--darker-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    position: relative;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
}

.pricing-card.featured {
    border-color: var(--primary-orange);
    box-shadow: var(--shadow-orange);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-orange);
    color: var(--light-text);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-orange);
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.period {
    font-size: 1rem;
    color: var(--gray-text);
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
}

.features-list li {
    padding: 0.5rem 0;
    color: var(--gray-text);
    border-bottom: 1px solid rgba(255, 107, 53, 0.1);
}

.features-list li:last-child {
    border-bottom: none;
}

.pricing-cta {
    text-align: center;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 107, 53, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--gray-text);
    text-decoration: none;
    transition: var(--transition);
    display: block;
    padding: 0.25rem 0;
}

.footer-section a:hover {
    color: var(--primary-orange);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 107, 53, 0.1);
    color: var(--gray-text);
}

/* Additional Services */
.additional-services {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-item {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.service-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.service-item p {
    color: var(--gray-text);
    margin-bottom: 1.5rem;
}

.service-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-orange);
    margin-bottom: 1.5rem;
}

.service-price span {
    font-size: 1.5rem;
}

/* FAQ Section */
.faq-section {
    padding: 5rem 0;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.faq-item h3 {
    color: var(--primary-orange);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.faq-item p {
    color: var(--gray-text);
    line-height: 1.6;
}

/* Services Page Styles */
.service-detail {
    padding: 5rem 0;
}

.service-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.service-content.reverse {
    direction: rtl;
}

.service-content.reverse > * {
    direction: ltr;
}

.service-info h2 {
    font-size: 2.5rem;
    color: var(--primary-orange);
    margin-bottom: 1.5rem;
}

.service-info p {
    font-size: 1.125rem;
    color: var(--gray-text);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    color: var(--light-text);
    border-bottom: 1px solid rgba(255, 107, 53, 0.1);
}

.service-features li:last-child {
    border-bottom: none;
}

.service-features li i {
    color: var(--primary-orange);
    width: 20px;
    flex-shrink: 0;
}

.service-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.service-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.service-icon-large {
    width: 200px;
    height: 200px;
    background: var(--gradient-orange);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 4rem;
    color: var(--light-text);
    box-shadow: var(--shadow-orange);
    animation: pulse 3s ease-in-out infinite;
}

/* Why Choose Us Section */
.why-choose {
    padding: 5rem 0;
}

.why-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.why-item {
    text-align: center;
    padding: 2rem;
    background: var(--dark-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.why-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.why-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.why-item h3 {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.why-item p {
    color: var(--gray-text);
}

/* Contact Page Styles */
.contact-methods {
    padding: 5rem 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.contact-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1.5rem;
}

.contact-card h3 {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--gray-text);
    margin-bottom: 1.5rem;
}

.contact-info {
    margin-bottom: 2rem;
}

.contact-info p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.contact-info i {
    color: var(--primary-orange);
    width: 16px;
}

/* Contact Forms */
.contact-forms {
    padding: 5rem 0;
}

.forms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.form-container {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.form-container h2 {
    color: var(--primary-orange);
    margin-bottom: 2rem;
    text-align: center;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: var(--light-text);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: var(--border-radius);
    background: var(--darker-bg);
    color: var(--light-text);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-orange);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Office Information */
.office-info {
    padding: 5rem 0;
}

.office-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.office-details h2 {
    color: var(--primary-orange);
    margin-bottom: 2rem;
    font-size: 2rem;
}

.office-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.office-icon {
    font-size: 1.5rem;
    color: var(--primary-orange);
    width: 40px;
    flex-shrink: 0;
}

.office-content h3 {
    color: var(--light-text);
    margin-bottom: 0.5rem;
}

.office-content p {
    color: var(--gray-text);
    line-height: 1.6;
}

.office-content a {
    color: var(--primary-orange);
    text-decoration: none;
    transition: var(--transition);
}

.office-content a:hover {
    color: var(--accent-orange);
}

.office-map {
    background: var(--dark-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
    overflow: hidden;
}

.map-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--gray-text);
}

.map-placeholder i {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

/* Contact FAQ */
.contact-faq {
    padding: 5rem 0;
}

/* About Page Styles */
.company-story {
    padding: 5rem 0;
}

.story-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.story-text h2 {
    font-size: 2.5rem;
    color: var(--primary-orange);
    margin-bottom: 2rem;
}

.story-text p {
    color: var(--gray-text);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
}

.story-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--dark-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-orange);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-text);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Values Section */
.our-values {
    padding: 5rem 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.value-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1.5rem;
}

.value-card h3 {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--gray-text);
    line-height: 1.6;
}

/* Team Section */
.our-team {
    padding: 5rem 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
}

.member-photo {
    width: 120px;
    height: 120px;
    background: var(--gradient-orange);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1.5rem;
    font-size: 3rem;
    color: var(--light-text);
}

.team-member h3 {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 0.5rem;
}

.member-position {
    color: var(--primary-orange);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-description {
    color: var(--gray-text);
    line-height: 1.6;
}

/* Infrastructure Section */
.infrastructure {
    padding: 5rem 0;
}

.infrastructure-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.infrastructure-text h3 {
    color: var(--primary-orange);
    margin-bottom: 1rem;
    margin-top: 2rem;
}

.infrastructure-text h3:first-child {
    margin-top: 0;
}

.infrastructure-text p {
    color: var(--gray-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.infrastructure-features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--dark-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.feature-item:hover {
    border-color: var(--primary-orange);
    background: rgba(255, 107, 53, 0.05);
}

.feature-item i {
    color: var(--primary-orange);
    font-size: 1.5rem;
    width: 24px;
}

.feature-item span {
    color: var(--light-text);
}

/* Certifications */
.certifications {
    padding: 5rem 0;
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cert-item {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.cert-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.cert-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1.5rem;
}

.cert-item h3 {
    font-size: 1.25rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.cert-item p {
    color: var(--gray-text);
    font-size: 0.875rem;
}

/* CTA Section */
.cta-section {
    padding: 5rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    color: var(--light-text);
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Pricing Section Enhancements */
.pricing-section {
    padding: 5rem 0;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 0;
    color: var(--gray-text);
    border-bottom: 1px solid rgba(255, 107, 53, 0.1);
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list li i {
    color: var(--primary-orange);
    width: 16px;
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--darker-bg);
        flex-direction: column;
        padding: 1rem;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .nav-menu.active {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .pricing-cards {
        grid-template-columns: 1fr;
    }

    .additional-services {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    /* About page responsive */
    .story-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .story-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .infrastructure-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .office-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .forms-grid {
        grid-template-columns: 1fr;
    }

    /* Services page responsive */
    .service-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-content.reverse {
        direction: ltr;
    }

    .service-actions {
        justify-content: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .story-stats {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2rem;
    }

    .service-info h2 {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }
}
