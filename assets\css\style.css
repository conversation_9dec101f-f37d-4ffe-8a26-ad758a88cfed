/* Professional Hosting Provider Styles */
:root {
    --primary-orange: #ff6b35;
    --secondary-orange: #e55a2b;
    --dark-bg: #0a0a0a;
    --darker-bg: #000000;
    --card-bg: #1a1a1a;
    --border-color: #333333;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --gradient-orange: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSys<PERSON><PERSON>ont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 700;
    font-size: 20px;
}

.logo img {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-orange);
}

.nav-item {
    position: relative;
}

.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    padding: 10px 0;
    margin-top: 10px;
}

.nav-item:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: rgba(255, 107, 53, 0.1);
    color: var(--primary-orange);
}

.lang-switcher {
    display: flex;
    gap: 5px;
}

.lang-btn {
    padding: 8px 12px;
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.lang-btn:hover,
.lang-btn.active {
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

.mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 24px;
    cursor: pointer;
}

/* Hero Section */
.hero {
    padding: 140px 0 100px;
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
    text-align: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    border-radius: var(--radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    gap: 8px;
}

.btn-primary {
    background: var(--gradient-orange);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-orange);
    border: 2px solid var(--primary-orange);
}

.btn-secondary:hover {
    background: var(--primary-orange);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

/* Sections */
section {
    padding: 80px 0;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Features */
.features {
    background: var(--card-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-orange);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-orange);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 28px;
    color: white;
}

.feature-card h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing */
.pricing {
    background: var(--darker-bg);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: 40px 30px;
    position: relative;
    transition: var(--transition);
}

.pricing-card.featured {
    border-color: var(--primary-orange);
    transform: scale(1.05);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-orange);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.pricing-card h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.pricing-card .price {
    margin-bottom: 30px;
}

.price-amount {
    font-size: 48px;
    font-weight: 700;
    color: var(--primary-orange);
}

.price-currency {
    font-size: 24px;
    vertical-align: top;
}

.price-period {
    font-size: 16px;
    color: var(--text-secondary);
    margin-left: 5px;
}

.features-list {
    list-style: none;
    margin-bottom: 30px;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list li i {
    color: var(--primary-orange);
    width: 16px;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-orange);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        margin: 10px 20px;
        padding: 20px;
        flex-direction: column;
        gap: 20px;
    }

    .nav-menu.active {
        display: flex;
    }

    .dropdown {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
        margin-top: 10px;
        padding: 0;
    }

    .hero {
        padding: 120px 0 80px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
}
