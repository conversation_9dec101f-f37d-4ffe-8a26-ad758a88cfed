/* CSS Variables for Orange-Dark Theme */
:root {
    --primary-orange: #ff6b35;
    --secondary-orange: #f7931e;
    --dark-bg: #1a1a1a;
    --darker-bg: #0f0f0f;
    --light-text: #ffffff;
    --gray-text: #cccccc;
    --dark-text: #333333;
    --accent-orange: #ff8c42;
    --gradient-orange: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    --shadow-orange: 0 4px 20px rgba(255, 107, 53, 0.3);
    --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.5);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--dark-bg);
    color: var(--light-text);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--gradient-dark);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-orange);
    text-decoration: none;
}

.logo img {
    height: 40px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover {
    color: var(--primary-orange);
    background: rgba(255, 107, 53, 0.1);
}

/* Dropdown Menu */
.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--darker-bg);
    min-width: 200px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-dark);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.nav-item:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: rgba(255, 107, 53, 0.1);
    color: var(--primary-orange);
}

/* Language Switcher */
.language-switcher {
    display: flex;
    gap: 0.5rem;
}

.lang-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid var(--primary-orange);
    color: var(--primary-orange);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.lang-btn:hover,
.lang-btn.active {
    background: var(--primary-orange);
    color: var(--light-text);
}

/* Mobile Menu Toggle */
.mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: var(--gradient-dark);
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding-top: 80px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    background: var(--gradient-orange);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-text);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-orange);
    color: var(--light-text);
    box-shadow: var(--shadow-orange);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-orange);
    border: 2px solid var(--primary-orange);
}

.btn-secondary:hover {
    background: var(--primary-orange);
    color: var(--light-text);
}

.btn-outline {
    background: transparent;
    color: var(--light-text);
    border: 2px solid var(--light-text);
}

.btn-outline:hover {
    background: var(--light-text);
    color: var(--dark-bg);
}

/* Sections */
section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient-orange);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Section */
.features {
    background: var(--darker-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--dark-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
    border-color: var(--primary-orange);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.feature-card p {
    color: var(--gray-text);
}

/* Pricing Section */
.pricing-preview {
    background: var(--dark-bg);
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.pricing-card {
    background: var(--darker-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    position: relative;
    border: 1px solid rgba(255, 107, 53, 0.1);
    transition: var(--transition);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-orange);
}

.pricing-card.featured {
    border-color: var(--primary-orange);
    box-shadow: var(--shadow-orange);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-orange);
    color: var(--light-text);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-orange);
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.period {
    font-size: 1rem;
    color: var(--gray-text);
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
}

.features-list li {
    padding: 0.5rem 0;
    color: var(--gray-text);
    border-bottom: 1px solid rgba(255, 107, 53, 0.1);
}

.features-list li:last-child {
    border-bottom: none;
}

.pricing-cta {
    text-align: center;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 107, 53, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--primary-orange);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--gray-text);
    text-decoration: none;
    transition: var(--transition);
    display: block;
    padding: 0.25rem 0;
}

.footer-section a:hover {
    color: var(--primary-orange);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 107, 53, 0.1);
    color: var(--gray-text);
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--darker-bg);
        flex-direction: column;
        padding: 1rem;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .pricing-cards {
        grid-template-columns: 1fr;
    }
}
