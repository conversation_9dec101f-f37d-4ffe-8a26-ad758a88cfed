// Component Loader - для модульной загрузки компонентов
class ComponentLoader {
    constructor() {
        this.loadedComponents = new Set();
    }

    async loadComponent(elementId, componentPath) {
        if (this.loadedComponents.has(componentPath)) {
            return;
        }

        try {
            const response = await fetch(componentPath);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${componentPath}`);
            }
            
            const html = await response.text();
            const element = document.getElementById(elementId);
            
            if (element) {
                element.innerHTML = html;
                this.loadedComponents.add(componentPath);
                
                // Trigger custom event for component loaded
                document.dispatchEvent(new CustomEvent('componentLoaded', {
                    detail: { elementId, componentPath }
                }));
            }
        } catch (error) {
            console.error('Error loading component:', error);
        }
    }

    async loadAllComponents() {
        const components = [
            { id: 'header-placeholder', path: 'components/header.html' },
            { id: 'footer-placeholder', path: 'components/footer.html' }
        ];

        const loadPromises = components.map(component => 
            this.loadComponent(component.id, component.path)
        );

        await Promise.all(loadPromises);
    }
}

// Mobile Menu Handler
class MobileMenu {
    constructor() {
        this.isOpen = false;
        this.init();
    }

    init() {
        document.addEventListener('componentLoaded', () => {
            this.bindEvents();
        });
    }

    bindEvents() {
        const toggle = document.getElementById('mobile-toggle');
        const menu = document.getElementById('nav-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', () => this.toggleMenu());
            
            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!toggle.contains(e.target) && !menu.contains(e.target)) {
                    this.closeMenu();
                }
            });

            // Close menu when clicking on menu links
            menu.addEventListener('click', (e) => {
                if (e.target.classList.contains('nav-link')) {
                    this.closeMenu();
                }
            });
        }
    }

    toggleMenu() {
        const menu = document.getElementById('nav-menu');
        const toggle = document.getElementById('mobile-toggle');
        
        if (menu && toggle) {
            this.isOpen = !this.isOpen;
            menu.classList.toggle('active', this.isOpen);
            
            // Change icon
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.className = this.isOpen ? 'fas fa-times' : 'fas fa-bars';
            }
        }
    }

    closeMenu() {
        const menu = document.getElementById('nav-menu');
        const toggle = document.getElementById('mobile-toggle');
        
        if (menu && toggle && this.isOpen) {
            this.isOpen = false;
            menu.classList.remove('active');
            
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
    }
}

// Smooth Scroll Handler
class SmoothScroll {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
                    const targetPosition = targetElement.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }
        });
    }
}

// Scroll Animation Observer
class ScrollAnimations {
    constructor() {
        this.observer = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                (entries) => this.handleIntersection(entries),
                {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                }
            );

            this.observeElements();
        }
    }

    observeElements() {
        const elements = document.querySelectorAll('.scroll-animate');
        elements.forEach(el => {
            if (this.observer) {
                this.observer.observe(el);
            }
        });
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
                if (this.observer) {
                    this.observer.unobserve(entry.target);
                }
            }
        });
    }
}

// Loading Screen
class LoadingScreen {
    constructor() {
        this.createLoadingScreen();
    }

    createLoadingScreen() {
        const loadingHTML = `
            <div id="loading-screen" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: var(--dark-bg);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                transition: opacity 0.5s ease;
            ">
                <div style="text-align: center;">
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 1rem; color: var(--gray-text);">Завантаження...</p>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', loadingHTML);
    }

    hide() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.remove();
            }, 500);
        }
    }
}

// Form Handler
class FormHandler {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener('submit', (e) => {
            const form = e.target.closest('form');
            if (form && form.classList.contains('ajax-form')) {
                e.preventDefault();
                this.handleFormSubmit(form);
            }
        });
    }

    async handleFormSubmit(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn?.textContent;

        try {
            // Show loading state
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<div class="loading-spinner" style="width: 20px; height: 20px;"></div>';
            }

            // Simulate form submission (replace with actual endpoint)
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Show success message
            this.showMessage('Форма успішно відправлена!', 'success');
            form.reset();

        } catch (error) {
            console.error('Form submission error:', error);
            this.showMessage('Помилка відправки форми. Спробуйте ще раз.', 'error');
        } finally {
            // Restore button state
            if (submitBtn && originalText) {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }
    }

    showMessage(message, type = 'info') {
        const messageHTML = `
            <div class="form-message form-message-${type}" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--primary-orange)' : '#e74c3c'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: var(--border-radius);
                box-shadow: var(--shadow-dark);
                z-index: 1000;
                animation: slideInRight 0.3s ease;
            ">
                ${message}
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', messageHTML);

        // Auto remove after 5 seconds
        setTimeout(() => {
            const messageEl = document.querySelector('.form-message');
            if (messageEl) {
                messageEl.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => messageEl.remove(), 300);
            }
        }, 5000);
    }
}

// Initialize all components
document.addEventListener('DOMContentLoaded', async () => {
    const loading = new LoadingScreen();
    const componentLoader = new ComponentLoader();
    
    try {
        // Load components
        await componentLoader.loadAllComponents();
        
        // Initialize other modules
        new MobileMenu();
        new SmoothScroll();
        new ScrollAnimations();
        new FormHandler();
        
        // Hide loading screen
        setTimeout(() => {
            loading.hide();
        }, 1000);
        
    } catch (error) {
        console.error('Error initializing components:', error);
        loading.hide();
    }
});

// Add CSS animations for messages
const messageStyles = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = messageStyles;
document.head.appendChild(styleSheet);
