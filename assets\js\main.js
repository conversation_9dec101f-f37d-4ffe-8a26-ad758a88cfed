// Main Application Controller
class SiteHostApp {
    constructor() {
        this.isLoaded = false;
        this.modules = {};
        this.init();
    }

    async init() {
        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize core modules
            await this.initializeModules();
            
            // Set up global event listeners
            this.setupGlobalEvents();
            
            // Mark as loaded
            this.isLoaded = true;
            
            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('siteHostReady'));
            
        } catch (error) {
            console.error('Error initializing SiteHost app:', error);
        }
    }

    async initializeModules() {
        // Initialize modules in order
        const moduleInitOrder = [
            'components',
            'translations', 
            'animations',
            'forms',
            'navigation',
            'performance'
        ];

        for (const moduleName of moduleInitOrder) {
            try {
                await this.initModule(moduleName);
            } catch (error) {
                console.warn(`Failed to initialize module ${moduleName}:`, error);
            }
        }
    }

    async initModule(moduleName) {
        switch (moduleName) {
            case 'components':
                // Components are already initialized by components.js
                break;
                
            case 'translations':
                // Translations are already initialized by translations.js
                break;
                
            case 'animations':
                // Animations are already initialized by animations.js
                break;
                
            case 'forms':
                this.modules.forms = new FormValidator();
                break;
                
            case 'navigation':
                this.modules.navigation = new NavigationManager();
                break;
                
            case 'performance':
                this.modules.performance = new PerformanceOptimizer();
                break;
        }
    }

    setupGlobalEvents() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.onPageHidden();
            } else {
                this.onPageVisible();
            }
        });

        // Handle window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.onWindowResize();
            }, 250);
        });

        // Handle scroll events (throttled)
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (!scrollTimeout) {
                scrollTimeout = setTimeout(() => {
                    this.onScroll();
                    scrollTimeout = null;
                }, 16); // ~60fps
            }
        });

        // Handle online/offline status
        window.addEventListener('online', () => this.onOnline());
        window.addEventListener('offline', () => this.onOffline());
    }

    onPageHidden() {
        // Pause animations and reduce activity when page is hidden
        if (this.modules.animations) {
            this.modules.animations.pause();
        }
    }

    onPageVisible() {
        // Resume animations when page becomes visible
        if (this.modules.animations) {
            this.modules.animations.resume();
        }
    }

    onWindowResize() {
        // Handle responsive adjustments
        this.updateViewportInfo();
        
        if (this.modules.navigation) {
            this.modules.navigation.handleResize();
        }
    }

    onScroll() {
        // Handle scroll-based features
        this.updateScrollProgress();
        
        if (this.modules.navigation) {
            this.modules.navigation.handleScroll();
        }
    }

    onOnline() {
        console.log('Connection restored');
        this.showConnectionStatus('online');
    }

    onOffline() {
        console.log('Connection lost');
        this.showConnectionStatus('offline');
    }

    updateViewportInfo() {
        // Update CSS custom properties with viewport info
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    updateScrollProgress() {
        // Update scroll progress indicator
        const scrollProgress = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
        document.documentElement.style.setProperty('--scroll-progress', `${scrollProgress}%`);
    }

    showConnectionStatus(status) {
        const statusEl = document.getElementById('connection-status') || this.createConnectionStatus();
        statusEl.className = `connection-status ${status}`;
        statusEl.textContent = status === 'online' ? 'З\'єднання відновлено' : 'Немає з\'єднання';
        
        if (status === 'online') {
            setTimeout(() => {
                statusEl.style.opacity = '0';
            }, 3000);
        } else {
            statusEl.style.opacity = '1';
        }
    }

    createConnectionStatus() {
        const statusEl = document.createElement('div');
        statusEl.id = 'connection-status';
        statusEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transition: opacity 0.3s ease;
            opacity: 0;
        `;
        document.body.appendChild(statusEl);
        return statusEl;
    }
}

// Form Validator Module
class FormValidator {
    constructor() {
        this.rules = {};
        this.init();
    }

    init() {
        this.setupValidationRules();
        this.bindFormEvents();
    }

    setupValidationRules() {
        this.rules = {
            email: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Введіть коректну email адресу'
            },
            phone: {
                pattern: /^[\+]?[0-9\s\-\(\)]{10,}$/,
                message: 'Введіть коректний номер телефону'
            },
            required: {
                test: (value) => value.trim().length > 0,
                message: 'Це поле обов\'язкове'
            }
        };
    }

    bindFormEvents() {
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('data-validate')) {
                this.validateField(e.target);
            }
        });

        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.hasAttribute('data-validate-form')) {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            }
        });
    }

    validateField(field) {
        const rules = field.getAttribute('data-validate').split(' ');
        const value = field.value;
        let isValid = true;
        let errorMessage = '';

        for (const rule of rules) {
            if (rule === 'required' && !this.rules.required.test(value)) {
                isValid = false;
                errorMessage = this.rules.required.message;
                break;
            } else if (this.rules[rule] && value && !this.rules[rule].pattern.test(value)) {
                isValid = false;
                errorMessage = this.rules[rule].message;
                break;
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    validateForm(form) {
        const fields = form.querySelectorAll('[data-validate]');
        let isFormValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isFormValid = false;
            }
        });

        return isFormValid;
    }

    showFieldValidation(field, isValid, message) {
        // Remove existing error
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add/remove valid/invalid classes
        field.classList.toggle('field-valid', isValid);
        field.classList.toggle('field-invalid', !isValid);

        // Show error message if invalid
        if (!isValid && message) {
            const errorEl = document.createElement('div');
            errorEl.className = 'field-error';
            errorEl.textContent = message;
            errorEl.style.cssText = `
                color: #e74c3c;
                font-size: 0.875rem;
                margin-top: 0.25rem;
            `;
            field.parentNode.appendChild(errorEl);
        }
    }
}

// Navigation Manager Module
class NavigationManager {
    constructor() {
        this.currentSection = '';
        this.init();
    }

    init() {
        this.setupActiveNavigation();
        this.setupStickyHeader();
    }

    setupActiveNavigation() {
        // Highlight active navigation item based on current page
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href.replace('.html', ''))) {
                link.classList.add('active');
            }
        });
    }

    setupStickyHeader() {
        const header = document.querySelector('.header');
        if (!header) return;

        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide header when scrolling down, show when scrolling up
            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
        });
    }

    handleResize() {
        // Handle navigation responsive changes
        const mobileToggle = document.getElementById('mobile-toggle');
        const navMenu = document.getElementById('nav-menu');
        
        if (window.innerWidth > 768 && navMenu) {
            navMenu.classList.remove('active');
            if (mobileToggle) {
                const icon = mobileToggle.querySelector('i');
                if (icon) icon.className = 'fas fa-bars';
            }
        }
    }

    handleScroll() {
        // Update active section based on scroll position
        this.updateActiveSection();
    }

    updateActiveSection() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                if (this.currentSection !== sectionId) {
                    this.currentSection = sectionId;
                    this.highlightNavItem(sectionId);
                }
            }
        });
    }

    highlightNavItem(sectionId) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
                link.classList.add('active');
            }
        });
    }
}

// Performance Optimizer Module
class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.lazyLoadImages();
        this.preloadCriticalResources();
        this.optimizeAnimations();
    }

    lazyLoadImages() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    preloadCriticalResources() {
        // Preload critical CSS and fonts
        const criticalResources = [
            { href: 'assets/css/style.css', as: 'style' },
            { href: 'assets/css/animations.css', as: 'style' }
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }

    optimizeAnimations() {
        // Reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.documentElement.style.setProperty('--animation-duration', '0.1s');
        }
    }
}

// Initialize the application
window.siteHostApp = new SiteHostApp();

// Add CSS for form validation
const validationStyles = `
    .field-valid {
        border-color: var(--primary-orange) !important;
    }
    
    .field-invalid {
        border-color: #e74c3c !important;
    }
    
    .header.scrolled {
        background: rgba(26, 26, 26, 0.95);
        backdrop-filter: blur(10px);
    }
    
    .connection-status.online {
        background: var(--primary-orange);
    }
    
    .connection-status.offline {
        background: #e74c3c;
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = validationStyles;
document.head.appendChild(styleSheet);
