// Professional Hosting Site JavaScript
class SiteHostApp {
    constructor() {
        this.currentLang = 'uk';
        this.translations = {};
        this.init();
    }

    init() {
        this.loadTranslations();
        this.setupMobileMenu();
        this.setupLanguageSwitcher();
        this.setupScrollEffects();
        this.setupAnimations();
        this.setInitialLanguage();
    }

    loadTranslations() {
        this.translations = {
            uk: {
                // Navigation
                'nav.home': 'Головна',
                'nav.pricing': 'Тарифи',
                'nav.services': 'Послуги',
                'nav.about': 'Про нас',
                'nav.contact': 'Контакти',
                'nav.hosting': 'Хостинг',
                'nav.vps': 'VPS',
                'nav.dedicated': 'Виділені сервери',
                'nav.domains': 'Домени',
                'nav.ssl': 'SSL сертифікати',

                // Hero Section
                'hero.title': 'Надійний хостинг для вашого успіху',
                'hero.subtitle': 'Швидкі SSD сервери, 99.9% аптайм та професійна підтримка 24/7. Почніть свій проект сьогодні.',
                'hero.get_started': 'Почати зараз',
                'hero.learn_more': 'Дізнатися більше',

                // Features
                'features.title': 'Чому обирають нас',
                'features.subtitle': 'Ми надаємо найкращі рішення для вашого онлайн бізнесу',
                'features.speed.title': 'Швидкість',
                'features.speed.description': 'SSD NVMe накопичувачі та CDN забезпечують блискавичну швидкість завантаження ваших сайтів.',
                'features.security.title': 'Безпека',
                'features.security.description': 'Багаторівневий захист, SSL сертифікати, DDoS захист та регулярні резервні копії.',
                'features.support.title': 'Підтримка 24/7',
                'features.support.description': 'Професійна технічна підтримка цілодобово. Швидкі відповіді та кваліфіковані рішення.',
                'features.uptime.title': '99.9% Аптайм',
                'features.uptime.description': 'Гарантована стабільність роботи з SLA угодою та компенсацією за простої.',

                // Pricing
                'pricing.title': 'Наші тарифи',
                'pricing.subtitle': 'Оберіть оптимальний план для ваших потреб',
                'pricing.period': '/міс',
                'pricing.popular': 'Популярний',
                'pricing.choose': 'Обрати план',
                'pricing.view_all': 'Переглянути всі тарифи',
                'pricing.starter.name': 'Стартовий',
                'pricing.starter.storage': '5 ГБ SSD простір',
                'pricing.starter.bandwidth': '100 ГБ трафік',
                'pricing.starter.domains': '1 домен',
                'pricing.starter.email': '5 email скриньок',
                'pricing.starter.ssl': 'Безкоштовний SSL',
                'pricing.business.name': 'Бізнес',
                'pricing.business.storage': '50 ГБ SSD простір',
                'pricing.business.bandwidth': 'Безлімітний трафік',
                'pricing.business.domains': '10 доменів',
                'pricing.business.email': 'Безлімітні email',
                'pricing.business.ssl': 'Безкоштовний SSL',
                'pricing.business.cdn': 'CDN включено',
                'pricing.enterprise.name': 'Корпоративний',
                'pricing.enterprise.storage': '200 ГБ SSD простір',
                'pricing.enterprise.bandwidth': 'Безлімітний трафік',
                'pricing.enterprise.domains': 'Безлімітні домени',
                'pricing.enterprise.email': 'Безлімітні email',
                'pricing.enterprise.ssl': 'Безкоштовний SSL',
                'pricing.enterprise.priority': 'Пріоритетна підтримка',

                // Footer
                'footer.company.title': 'Компанія',
                'footer.company.about': 'Про нас',
                'footer.company.contact': 'Контакти',
                'footer.company.careers': 'Кар\'єра',
                'footer.company.news': 'Новини',
                'footer.services.title': 'Послуги',
                'footer.services.hosting': 'Веб-хостинг',
                'footer.services.vps': 'VPS сервери',
                'footer.services.dedicated': 'Виділені сервери',
                'footer.services.domains': 'Домени',
                'footer.services.ssl': 'SSL сертифікати',
                'footer.support.title': 'Підтримка',
                'footer.support.docs': 'Документація',
                'footer.support.status': 'Статус сервісів',
                'footer.support.help': 'Центр допомоги',
                'footer.support.contact': 'Технічна підтримка',
                'footer.legal.title': 'Правова інформація',
                'footer.legal.terms': 'Оферта',
                'footer.legal.privacy': 'Політика конфіденційності',
                'footer.legal.payment': 'Правила оплати та повернення',
                'footer.legal.cookies': 'Політика cookies',
                'footer.rights': 'Всі права захищені.'
            },

            ru: {
                // Navigation
                'nav.home': 'Главная',
                'nav.pricing': 'Тарифы',
                'nav.services': 'Услуги',
                'nav.about': 'О нас',
                'nav.contact': 'Контакты',
                'nav.hosting': 'Хостинг',
                'nav.vps': 'VPS',
                'nav.dedicated': 'Выделенные серверы',
                'nav.domains': 'Домены',
                'nav.ssl': 'SSL сертификаты',

                // Hero Section
                'hero.title': 'Надежный хостинг для вашего успеха',
                'hero.subtitle': 'Быстрые SSD серверы, 99.9% аптайм и профессиональная поддержка 24/7. Начните свой проект сегодня.',
                'hero.get_started': 'Начать сейчас',
                'hero.learn_more': 'Узнать больше',

                // Features
                'features.title': 'Почему выбирают нас',
                'features.subtitle': 'Мы предоставляем лучшие решения для вашего онлайн бизнеса',
                'features.speed.title': 'Скорость',
                'features.speed.description': 'SSD NVMe накопители и CDN обеспечивают молниеносную скорость загрузки ваших сайтов.',
                'features.security.title': 'Безопасность',
                'features.security.description': 'Многоуровневая защита, SSL сертификаты, DDoS защита и регулярные резервные копии.',
                'features.support.title': 'Поддержка 24/7',
                'features.support.description': 'Профессиональная техническая поддержка круглосуточно. Быстрые ответы и квалифицированные решения.',
                'features.uptime.title': '99.9% Аптайм',
                'features.uptime.description': 'Гарантированная стабильность работы с SLA соглашением и компенсацией за простои.',

                // Pricing
                'pricing.title': 'Наши тарифы',
                'pricing.subtitle': 'Выберите оптимальный план для ваших нужд',
                'pricing.period': '/мес',
                'pricing.popular': 'Популярный',
                'pricing.choose': 'Выбрать план',
                'pricing.view_all': 'Посмотреть все тарифы',
                'pricing.starter.name': 'Стартовый',
                'pricing.starter.storage': '5 ГБ SSD пространство',
                'pricing.starter.bandwidth': '100 ГБ трафик',
                'pricing.starter.domains': '1 домен',
                'pricing.starter.email': '5 email ящиков',
                'pricing.starter.ssl': 'Бесплатный SSL',
                'pricing.business.name': 'Бизнес',
                'pricing.business.storage': '50 ГБ SSD пространство',
                'pricing.business.bandwidth': 'Безлимитный трафик',
                'pricing.business.domains': '10 доменов',
                'pricing.business.email': 'Безлимитные email',
                'pricing.business.ssl': 'Бесплатный SSL',
                'pricing.business.cdn': 'CDN включен',
                'pricing.enterprise.name': 'Корпоративный',
                'pricing.enterprise.storage': '200 ГБ SSD пространство',
                'pricing.enterprise.bandwidth': 'Безлимитный трафик',
                'pricing.enterprise.domains': 'Безлимитные домены',
                'pricing.enterprise.email': 'Безлимитные email',
                'pricing.enterprise.ssl': 'Бесплатный SSL',
                'pricing.enterprise.priority': 'Приоритетная поддержка',

                // Footer
                'footer.company.title': 'Компания',
                'footer.company.about': 'О нас',
                'footer.company.contact': 'Контакты',
                'footer.company.careers': 'Карьера',
                'footer.company.news': 'Новости',
                'footer.services.title': 'Услуги',
                'footer.services.hosting': 'Веб-хостинг',
                'footer.services.vps': 'VPS серверы',
                'footer.services.dedicated': 'Выделенные серверы',
                'footer.services.domains': 'Домены',
                'footer.services.ssl': 'SSL сертификаты',
                'footer.support.title': 'Поддержка',
                'footer.support.docs': 'Документация',
                'footer.support.status': 'Статус сервисов',
                'footer.support.help': 'Центр помощи',
                'footer.support.contact': 'Техническая поддержка',
                'footer.legal.title': 'Правовая информация',
                'footer.legal.terms': 'Оферта',
                'footer.legal.privacy': 'Политика конфиденциальности',
                'footer.legal.payment': 'Правила оплаты и возврата',
                'footer.legal.cookies': 'Политика cookies',
                'footer.rights': 'Все права защищены.'
            },

            en: {
                // Navigation
                'nav.home': 'Home',
                'nav.pricing': 'Pricing',
                'nav.services': 'Services',
                'nav.about': 'About',
                'nav.contact': 'Contact',
                'nav.hosting': 'Hosting',
                'nav.vps': 'VPS',
                'nav.dedicated': 'Dedicated Servers',
                'nav.domains': 'Domains',
                'nav.ssl': 'SSL Certificates',

                // Hero Section
                'hero.title': 'Reliable hosting for your success',
                'hero.subtitle': 'Fast SSD servers, 99.9% uptime and professional 24/7 support. Start your project today.',
                'hero.get_started': 'Get Started',
                'hero.learn_more': 'Learn More',

                // Features
                'features.title': 'Why choose us',
                'features.subtitle': 'We provide the best solutions for your online business',
                'features.speed.title': 'Speed',
                'features.speed.description': 'SSD NVMe drives and CDN ensure lightning-fast loading speeds for your websites.',
                'features.security.title': 'Security',
                'features.security.description': 'Multi-layered protection, SSL certificates, DDoS protection and regular backups.',
                'features.support.title': '24/7 Support',
                'features.support.description': 'Professional technical support around the clock. Fast responses and qualified solutions.',
                'features.uptime.title': '99.9% Uptime',
                'features.uptime.description': 'Guaranteed stability with SLA agreement and compensation for downtime.',

                // Pricing
                'pricing.title': 'Our pricing',
                'pricing.subtitle': 'Choose the optimal plan for your needs',
                'pricing.period': '/mo',
                'pricing.popular': 'Popular',
                'pricing.choose': 'Choose Plan',
                'pricing.view_all': 'View All Plans',
                'pricing.starter.name': 'Starter',
                'pricing.starter.storage': '5 GB SSD space',
                'pricing.starter.bandwidth': '100 GB traffic',
                'pricing.starter.domains': '1 domain',
                'pricing.starter.email': '5 email accounts',
                'pricing.starter.ssl': 'Free SSL',
                'pricing.business.name': 'Business',
                'pricing.business.storage': '50 GB SSD space',
                'pricing.business.bandwidth': 'Unlimited traffic',
                'pricing.business.domains': '10 domains',
                'pricing.business.email': 'Unlimited email',
                'pricing.business.ssl': 'Free SSL',
                'pricing.business.cdn': 'CDN included',
                'pricing.enterprise.name': 'Enterprise',
                'pricing.enterprise.storage': '200 GB SSD space',
                'pricing.enterprise.bandwidth': 'Unlimited traffic',
                'pricing.enterprise.domains': 'Unlimited domains',
                'pricing.enterprise.email': 'Unlimited email',
                'pricing.enterprise.ssl': 'Free SSL',
                'pricing.enterprise.priority': 'Priority support',

                // Footer
                'footer.company.title': 'Company',
                'footer.company.about': 'About Us',
                'footer.company.contact': 'Contact',
                'footer.company.careers': 'Careers',
                'footer.company.news': 'News',
                'footer.services.title': 'Services',
                'footer.services.hosting': 'Web Hosting',
                'footer.services.vps': 'VPS Servers',
                'footer.services.dedicated': 'Dedicated Servers',
                'footer.services.domains': 'Domains',
                'footer.services.ssl': 'SSL Certificates',
                'footer.support.title': 'Support',
                'footer.support.docs': 'Documentation',
                'footer.support.status': 'Service Status',
                'footer.support.help': 'Help Center',
                'footer.support.contact': 'Technical Support',
                'footer.legal.title': 'Legal',
                'footer.legal.terms': 'Terms of Service',
                'footer.legal.privacy': 'Privacy Policy',
                'footer.legal.payment': 'Payment & Refund Policy',
                'footer.legal.cookies': 'Cookie Policy',
                'footer.rights': 'All rights reserved.'
            }
        };
    }

    setupMobileMenu() {
        const toggle = document.getElementById('mobile-toggle');
        const menu = document.getElementById('nav-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', () => {
                menu.classList.toggle('active');
                const icon = toggle.querySelector('i');
                if (menu.classList.contains('active')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!toggle.contains(e.target) && !menu.contains(e.target)) {
                    menu.classList.remove('active');
                    toggle.querySelector('i').className = 'fas fa-bars';
                }
            });
        }
    }

    setupLanguageSwitcher() {
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const lang = btn.getAttribute('data-lang');
                this.switchLanguage(lang);
            });
        });
    }

    setupScrollEffects() {
        const header = document.querySelector('.header');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            lastScrollY = currentScrollY;
        });
    }

    setupAnimations() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animateElements = document.querySelectorAll('.feature-card, .pricing-card');
        animateElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }

    setInitialLanguage() {
        const savedLang = localStorage.getItem('preferred-language');
        const browserLang = navigator.language.substring(0, 2);
        
        let initialLang = 'uk';
        
        if (savedLang && this.translations[savedLang]) {
            initialLang = savedLang;
        } else if (this.translations[browserLang]) {
            initialLang = browserLang;
        }
        
        this.switchLanguage(initialLang);
    }

    switchLanguage(lang) {
        if (!this.translations[lang]) return;

        this.currentLang = lang;
        localStorage.setItem('preferred-language', lang);
        
        document.documentElement.lang = lang;
        
        // Update active language button
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-lang') === lang);
        });
        
        // Translate all elements
        this.translatePage();
    }

    translatePage() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.translations[this.currentLang][key];
            
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'submit') {
                    element.value = translation;
                } else if (element.hasAttribute('placeholder')) {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new SiteHostApp();
});
