// Animation Controller
class AnimationController {
    constructor() {
        this.animations = new Map();
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.init();
    }

    init() {
        if (!this.isReducedMotion) {
            this.initHeroAnimations();
            this.initScrollAnimations();
            this.initParticleSystem();
            this.initCounterAnimations();
        }
    }

    // Hero Section Animations
    initHeroAnimations() {
        const heroSection = document.querySelector('.hero');
        if (!heroSection) return;

        // Animate hero content on load
        const heroTitle = heroSection.querySelector('.hero-title');
        const heroSubtitle = heroSection.querySelector('.hero-subtitle');
        const heroButtons = heroSection.querySelector('.hero-buttons');

        if (heroTitle) {
            setTimeout(() => {
                heroTitle.classList.add('animate-fade-in-up');
            }, 500);
        }

        if (heroSubtitle) {
            setTimeout(() => {
                heroSubtitle.classList.add('animate-fade-in-up');
            }, 700);
        }

        if (heroButtons) {
            setTimeout(() => {
                heroButtons.classList.add('animate-fade-in-up');
            }, 900);
        }

        // Animate floating servers
        this.animateFloatingServers();
    }

    animateFloatingServers() {
        const serverIcons = document.querySelectorAll('.server-icon');
        serverIcons.forEach((icon, index) => {
            const delay = parseFloat(icon.getAttribute('data-delay')) || 0;
            
            setTimeout(() => {
                icon.style.opacity = '0.6';
                icon.style.animation = `float ${6 + index}s ease-in-out infinite`;
                icon.style.animationDelay = `${delay}s`;
            }, delay * 1000);
        });
    }

    // Scroll-triggered animations
    initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerElementAnimation(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animateElements = document.querySelectorAll(
            '.feature-card, .pricing-card, .section-title, .scroll-animate'
        );

        animateElements.forEach(el => observer.observe(el));
    }

    triggerElementAnimation(element) {
        if (element.classList.contains('feature-card')) {
            element.classList.add('animate-fade-in-up');
        } else if (element.classList.contains('pricing-card')) {
            element.classList.add('animate-scale-in');
        } else if (element.classList.contains('section-title')) {
            element.classList.add('animate-fade-in-up');
        } else {
            element.classList.add('animate-fade-in-up');
        }
    }

    // Particle System for Background
    initParticleSystem() {
        const particleContainer = document.querySelector('.hero-animation');
        if (!particleContainer) return;

        const particleCount = 20;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: var(--primary-orange);
                border-radius: 50%;
                opacity: 0.3;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: particle-float ${8 + Math.random() * 4}s linear infinite;
                animation-delay: ${Math.random() * 8}s;
            `;
            
            particleContainer.appendChild(particle);
        }
    }

    // Counter Animations
    initCounterAnimations() {
        const counters = document.querySelectorAll('.counter');
        
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => counterObserver.observe(counter));
    }

    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target')) || 0;
        const duration = parseInt(element.getAttribute('data-duration')) || 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        };

        updateCounter();
    }

    // Page-specific animations
    initPageAnimations(page) {
        switch (page) {
            case 'pricing':
                this.initPricingAnimations();
                break;
            case 'services':
                this.initServicesAnimations();
                break;
            case 'contact':
                this.initContactAnimations();
                break;
            case 'about':
                this.initAboutAnimations();
                break;
        }
    }

    initPricingAnimations() {
        const pricingSection = document.querySelector('.pricing-animation');
        if (pricingSection) {
            // Add rotating background gradient
            pricingSection.style.position = 'relative';
            pricingSection.style.overflow = 'hidden';
        }
    }

    initServicesAnimations() {
        // Create floating particles for services page
        const servicesContainer = document.querySelector('.services-animation');
        if (!servicesContainer) return;

        const particleCount = 15;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'service-particle';
            particle.style.cssText = `
                position: absolute;
                width: 3px;
                height: 3px;
                background: var(--accent-orange);
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                animation: particle-float ${10 + Math.random() * 5}s linear infinite;
                animation-delay: ${Math.random() * 10}s;
            `;
            
            servicesContainer.appendChild(particle);
        }
    }

    initContactAnimations() {
        // Create wave animation for contact page
        const contactContainer = document.querySelector('.contact-animation');
        if (!contactContainer) return;

        for (let i = 0; i < 3; i++) {
            const wave = document.createElement('div');
            wave.className = 'wave';
            wave.style.animationDelay = `${i * -5}s`;
            contactContainer.appendChild(wave);
        }
    }

    initAboutAnimations() {
        // Create tech grid animation for about page
        const aboutContainer = document.querySelector('.about-animation');
        if (aboutContainer) {
            const techGrid = document.createElement('div');
            techGrid.className = 'tech-grid';
            aboutContainer.appendChild(techGrid);
        }
    }

    // Utility methods
    addHoverAnimations() {
        // Add hover animations to interactive elements
        const hoverElements = document.querySelectorAll('.btn, .feature-card, .pricing-card');
        
        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                if (!this.isReducedMotion) {
                    element.style.transform = 'translateY(-5px)';
                    element.style.transition = 'transform 0.3s ease';
                }
            });

            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translateY(0)';
            });
        });
    }

    // Parallax effect for hero section
    initParallax() {
        const hero = document.querySelector('.hero');
        if (!hero) return;

        window.addEventListener('scroll', () => {
            if (this.isReducedMotion) return;
            
            const scrolled = window.pageYOffset;
            const parallaxElements = hero.querySelectorAll('.server-icon');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });
    }

    // Typewriter effect
    typeWriter(element, text, speed = 100) {
        if (this.isReducedMotion) {
            element.textContent = text;
            return;
        }

        element.textContent = '';
        let i = 0;
        
        const typeInterval = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(typeInterval);
            }
        }, speed);
    }

    // Stagger animation for multiple elements
    staggerAnimation(elements, animationClass, delay = 100) {
        elements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add(animationClass);
            }, index * delay);
        });
    }

    // Clean up animations
    destroy() {
        this.animations.forEach(animation => {
            if (animation.cancel) {
                animation.cancel();
            }
        });
        this.animations.clear();
    }
}

// Page-specific animation initialization
class PageAnimations {
    constructor() {
        this.controller = new AnimationController();
        this.initPageSpecific();
    }

    initPageSpecific() {
        const page = this.getCurrentPage();
        this.controller.initPageAnimations(page);
        this.controller.addHoverAnimations();
        this.controller.initParallax();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('pricing')) return 'pricing';
        if (path.includes('services')) return 'services';
        if (path.includes('contact')) return 'contact';
        if (path.includes('about')) return 'about';
        return 'home';
    }
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pageAnimations = new PageAnimations();
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.pageAnimations) {
        window.pageAnimations.controller.destroy();
    }
});
