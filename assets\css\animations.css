/* Hero Animation Styles */
.hero-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
}

.floating-servers {
    position: absolute;
    width: 100%;
    height: 100%;
}

.server-icon {
    position: absolute;
    font-size: 3rem;
    color: var(--primary-orange);
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

.server-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.server-icon:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 1.5s;
}

.server-icon:nth-child(3) {
    top: 30%;
    right: 30%;
    animation-delay: 3s;
}

.server-icon:nth-child(4) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4.5s;
}

.connection-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.connection-svg {
    width: 100%;
    height: 100%;
    opacity: 0.2;
}

.connection-path {
    fill: none;
    stroke: var(--primary-orange);
    stroke-width: 2;
    stroke-dasharray: 10, 5;
    animation: dash 8s linear infinite;
}

/* Keyframe Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
    }
    50% {
        transform: translateY(-10px) rotate(-5deg);
    }
    75% {
        transform: translateY(-15px) rotate(3deg);
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: -100;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-glow {
    animation: glow 3s ease-in-out infinite;
}

/* Staggered Animation Delays */
.animate-delay-1 {
    animation-delay: 0.1s;
}

.animate-delay-2 {
    animation-delay: 0.2s;
}

.animate-delay-3 {
    animation-delay: 0.3s;
}

.animate-delay-4 {
    animation-delay: 0.4s;
}

.animate-delay-5 {
    animation-delay: 0.5s;
}

/* Page-specific Animations */

/* Pricing Page Animations */
.pricing-animation {
    position: relative;
    overflow: hidden;
}

.pricing-animation::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Services Page Animations */
.services-animation {
    position: relative;
}

.service-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-orange);
    border-radius: 50%;
    animation: particle-float 8s linear infinite;
}

.particle:nth-child(odd) {
    animation-duration: 10s;
}

.particle:nth-child(even) {
    animation-duration: 12s;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Contact Page Animations */
.contact-animation {
    position: relative;
}

.contact-waves {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.wave {
    position: absolute;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.1), transparent);
    animation: wave-move 15s ease-in-out infinite;
}

.wave:nth-child(2) {
    animation-delay: -5s;
    animation-duration: 20s;
}

.wave:nth-child(3) {
    animation-delay: -10s;
    animation-duration: 25s;
}

@keyframes wave-move {
    0%, 100% {
        transform: translateX(-50%) translateY(-50%) rotate(0deg);
    }
    50% {
        transform: translateX(-50%) translateY(-50%) rotate(180deg);
    }
}

/* About Page Animations */
.about-animation {
    position: relative;
}

.tech-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image: 
        linear-gradient(rgba(255, 107, 53, 0.3) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 107, 53, 0.3) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

/* Scroll-triggered Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.scroll-animate.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 107, 53, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-orange);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

/* Text Animations */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-orange);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-orange);
    }
}

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .server-icon {
        font-size: 2rem;
    }
    
    .floating-servers .server-icon {
        animation-duration: 4s;
    }
    
    .connection-path {
        stroke-width: 1;
    }
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
